import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import { KubernetesService } from './kubernetes-service';

export interface ZapReportData {
  html: string;
  json?: any;
  timestamp: string;
  testCaseId: string;
  clientId: string;
  vulnerabilities: ZapVulnerability[];
  summary: ZapSummary;
  scanContext: ZapScanContext;
}

export interface ZapVulnerability {
  name: string;
  risk: 'High' | 'Medium' | 'Low' | 'Informational';
  confidence: string;
  description: string;
  solution?: string;
  reference?: string;
  instances: ZapInstance[];
}

export interface ZapInstance {
  uri: string;
  method: string;
  param?: string;
  evidence?: string;
}

export interface ZapSummary {
  totalIssues: number;
  highRisk: number;
  mediumRisk: number;
  lowRisk: number;
  informational: number;
  scanDuration?: number;
  urlsScanned?: number;
}

export interface ZapScanContext {
  sites: string[];
  urls: string[];
  hosts: string[];
  targetUrl?: string;
  scanStartTime?: string;
  scanEndTime?: string;
  spiderResults?: any;
  activeScanResults?: any;
}

export interface ZapContainerInfo {
  containerId: string;
  containerName: string;
  port: number;
  host: string;
  clientId: string;
  createdAt: Date;
}

export interface ZapJobInfo {
  jobName: string;
  serviceName: string;
  namespace: string;
  port: number;
  host: string;
  clientId: string;
  createdAt: Date;
}

export class ZapService {
  private static readonly ZAP_API_KEY = process.env.ZAP_API_KEY || 'AgentqSuperAI';
  private static readonly BACKEND_URL = process.env.AGENTQ_API_URL || process.env.BACKEND_URL || 'http://localhost:3010';

  // Track active ZAP Jobs by CLIENT_ID for complete isolation
  private static activeJobs: Map<string, ZapJobInfo> = new Map();

  // Legacy container tracking for backward compatibility during migration
  private static activeContainers: Map<string, ZapContainerInfo> = new Map();

  // Use Kubernetes Jobs instead of port allocation
  private static readonly USE_KUBERNETES = process.env.USE_KUBERNETES !== 'false'; // Default to true

  // Legacy port range (only used when USE_KUBERNETES=false)
  private static readonly ZAP_PORT_RANGE_START = 9001;
  private static readonly ZAP_PORT_RANGE_END = 10000;
  private static usedPorts: Set<number> = new Set();

  // Flag to track if startup cleanup has been performed
  private static startupCleanupPerformed = false;

  // Periodic cleanup interval
  private static cleanupInterval: NodeJS.Timeout | null = null;

  // ZAP operation queue to prevent concurrent scans from interfering
  private static zapOperationQueue: Array<() => Promise<any>> = [];
  private static isZapBusy = false;

  /**
   * Clean up any orphaned ZAP resources on server startup
   * This handles both Kubernetes Jobs and Docker containers left behind from previous server crashes/restarts
   * SAFETY: Only removes resources that are truly orphaned (older than 5 minutes)
   */
  static async performStartupCleanup(): Promise<void> {
    if (this.startupCleanupPerformed) {
      return;
    }

    try {
      if (this.USE_KUBERNETES) {
        console.log('🧹 Performing SAFE startup cleanup of orphaned ZAP Jobs...');
        await KubernetesService.cleanupOrphanedJobs(5); // 5 minutes
      } else {
        console.log('🧹 Performing SAFE startup cleanup of orphaned ZAP containers...');
        await this.performDockerStartupCleanup();
      }

      // Clear the active resources maps since we're starting fresh
      this.activeJobs.clear();
      this.activeContainers.clear();
      this.usedPorts.clear();

      this.startupCleanupPerformed = true;

      // Start periodic cleanup for better reliability
      this.startPeriodicCleanup();
    } catch (error) {
      console.error('⚠️ Error during startup cleanup:', error);
      this.startupCleanupPerformed = true; // Mark as performed even on error to avoid retries
    }
  }

  /**
   * Perform Docker container cleanup (legacy support)
   */
  private static async performDockerStartupCleanup(): Promise<void> {
    const { exec } = require('child_process');
    // Find containers older than 5 minutes to avoid removing active test containers
    const findCommand = 'docker ps -a --filter "name=zap-scan-" --format "{{.Names}} {{.CreatedAt}}"';

    const containerInfo = await new Promise<Array<{name: string, age: number}>>((resolve) => {
      exec(findCommand, (error: any, stdout: string, _stderr: string) => {
        if (error) {
          console.log('📝 No ZAP containers found during startup');
          resolve([]);
        } else {
          const lines = stdout.trim().split('\n').filter(line => line.length > 0);
          const containers = lines.map(line => {
            const parts = line.split(' ');
            const name = parts[0];
            const createdAt = parts.slice(1).join(' ');
            const createdTime = new Date(createdAt).getTime();
            const now = Date.now();
            const ageMinutes = (now - createdTime) / (1000 * 60);
            return { name, age: ageMinutes };
          }).filter(container => container.age > 5); // Only containers older than 5 minutes
          resolve(containers);
        }
      });
    });

    if (containerInfo.length > 0) {
      console.log(`🗑️ Found ${containerInfo.length} truly orphaned ZAP containers (>5min old):`);
      containerInfo.forEach(c => console.log(`   - ${c.name} (${c.age.toFixed(1)} minutes old)`));

      for (const container of containerInfo) {
        try {
          const removeCommand = `docker rm -f ${container.name}`;
          await new Promise<void>((resolve) => {
            exec(removeCommand, (error: any, _stdout: string, _stderr: string) => {
              if (error) {
                console.warn(`⚠️ Error removing orphaned container ${container.name}:`, error.message);
              } else {
                console.log(`✅ Removed orphaned ZAP container: ${container.name} (${container.age.toFixed(1)}min old)`);
              }
              resolve();
            });
          });
        } catch (error) {
          console.warn(`⚠️ Error removing orphaned container ${container.name}:`, error);
        }
      }

      console.log('✅ SAFE Docker startup cleanup completed - only truly orphaned containers removed');
    } else {
      console.log('📝 No orphaned ZAP containers found during startup (containers <5min old are preserved)');
    }
  }

  /**
   * Start periodic cleanup to handle orphaned resources
   */
  private static startPeriodicCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Run cleanup every 5 minutes
    this.cleanupInterval = setInterval(async () => {
      try {
        if (this.USE_KUBERNETES) {
          console.log('🔄 Running periodic ZAP Jobs cleanup...');
          await KubernetesService.cleanupOrphanedJobs(10); // 10 minutes
        } else {
          console.log('🔄 Running periodic ZAP container cleanup...');
          await this.performPeriodicDockerCleanup();
        }
      } catch (error) {
        console.error('❌ Error during periodic cleanup:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    const resourceType = this.USE_KUBERNETES ? 'Jobs' : 'containers';
    console.log(`✅ Periodic ZAP ${resourceType} cleanup started (every 5 minutes)`);
  }

  /**
   * Perform periodic cleanup of orphaned Docker containers (legacy support)
   */
  private static async performPeriodicDockerCleanup(): Promise<void> {
    const { exec } = require('child_process');

    // Find containers older than 10 minutes that might be orphaned
    const findCommand = `docker ps -a --filter "name=zap-scan-" --format "{{.Names}} {{.CreatedAt}}"`;

    try {
      const containerInfo = await new Promise<Array<{name: string, age: number}>>((resolve) => {
        exec(findCommand, (error: any, stdout: string, _stderr: string) => {
          if (error) {
            console.log('📝 No ZAP containers found during periodic cleanup');
            resolve([]);
          } else {
            const lines = stdout.trim().split('\n').filter(line => line.length > 0);
            const containers = lines.map(line => {
              const parts = line.split(' ');
              const name = parts[0];
              const createdAt = parts.slice(1).join(' ');
              const createdTime = new Date(createdAt).getTime();
              const now = Date.now();
              const ageMinutes = (now - createdTime) / (1000 * 60);
              return { name, age: ageMinutes };
            }).filter(container => container.age > 10); // Only containers older than 10 minutes
            resolve(containers);
          }
        });
      });

      if (containerInfo.length > 0) {
        console.log(`🧹 Found ${containerInfo.length} potentially orphaned ZAP containers during periodic cleanup`);

        for (const container of containerInfo) {
          // Extract clientId from container name
          const clientIdMatch = container.name.match(/zap-scan-(.+)/);
          if (clientIdMatch) {
            const clientId = clientIdMatch[1];

            // Check if this container is still tracked as active
            if (!this.activeContainers.has(clientId)) {
              console.log(`🗑️ Removing orphaned container: ${container.name} (${container.age.toFixed(1)}min old)`);

              try {
                const removeCommand = `docker rm -f ${container.name}`;
                await new Promise<void>((resolve) => {
                  exec(removeCommand, (error: any, _stdout: string, _stderr: string) => {
                    if (error) {
                      console.warn(`⚠️ Error removing orphaned container ${container.name}:`, error.message);
                    } else {
                      console.log(`✅ Orphaned container removed: ${container.name}`);
                    }
                    resolve();
                  });
                });
              } catch (error) {
                console.warn(`⚠️ Error removing orphaned container ${container.name}:`, error);
              }
            } else {
              console.log(`📝 Container ${container.name} is still active, skipping cleanup`);
            }
          }
        }
      } else {
        console.log('📝 No orphaned ZAP containers found during periodic cleanup');
      }
    } catch (error) {
      console.error('❌ Error during periodic cleanup:', error);
    }
  }

  /**
   * Queue ZAP operations to prevent concurrent scan interference
   * This ensures only one ZAP operation runs at a time
   */
  private static async queueZapOperation<T>(operation: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.zapOperationQueue.push(async () => {
        try {
          const result = await operation();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processZapQueue();
    });
  }

  /**
   * Process the ZAP operation queue
   */
  private static async processZapQueue(): Promise<void> {
    if (this.isZapBusy || this.zapOperationQueue.length === 0) {
      return;
    }

    this.isZapBusy = true;
    const operation = this.zapOperationQueue.shift();

    if (operation) {
      try {
        await operation();
      } catch (error) {
        console.error('ZAP operation failed:', error);
      }
    }

    this.isZapBusy = false;

    // Process next operation if any
    if (this.zapOperationQueue.length > 0) {
      setTimeout(() => this.processZapQueue(), 100); // Small delay between operations
    }
  }

  /**
   * Create dedicated ZAP container for CLIENT_ID complete isolation
   * This prevents reports from different users/companies from mixing
   */
  private static async createZapContainer(clientId: string): Promise<ZapContainerInfo | null> {
    try {
      console.log(`🐳 Creating dedicated ZAP container for CLIENT_ID: ${clientId}`);

      // Allocate dynamic port for this container
      const zapPort = await this.allocatePort();
      if (!zapPort) {
        console.error(`❌ No available ports for ZAP container (CLIENT_ID: ${clientId})`);
        return null;
      }

      const containerName = `zap-scan-${clientId}`;

      // Create ZAP container with dedicated port (using same port inside and outside container)
      const { exec } = require('child_process');
      const dockerCommand = `docker run -d --name ${containerName} -p ${zapPort}:${zapPort} -i asia-southeast2-docker.pkg.dev/agentq-464900/agentq/zaproxy:stable zap.sh -daemon -host 0.0.0.0 -port ${zapPort} -config api.addrs.addr.name=.* -config api.addrs.addr.regex=true -config api.key=${this.ZAP_API_KEY}`;

      console.log(`🚀 Executing Docker command: ${dockerCommand}`);

      const containerId = await new Promise<string>((resolve, reject) => {
        exec(dockerCommand, (error: any, stdout: string, stderr: string) => {
          if (error) {
            console.error(`❌ Docker container creation failed:`, error);
            reject(error);
            return;
          }

          const containerId = stdout.trim();
          console.log(`✅ ZAP container created: ${containerId} (port: ${zapPort})`);
          resolve(containerId);
        });
      });

      const containerInfo: ZapContainerInfo = {
        containerId,
        containerName,
        port: zapPort,
        host: `http://localhost:${zapPort}`,
        clientId,
        createdAt: new Date()
      };

      // Wait for ZAP container to be ready
      await this.waitForZapReady(containerInfo);

      // Track active container
      this.activeContainers.set(clientId, containerInfo);
      console.log(`🔒 ZAP container ready for CLIENT_ID ${clientId}: ${containerInfo.host}`);

      return containerInfo;
    } catch (error) {
      console.error(`❌ Error creating ZAP container for CLIENT_ID ${clientId}:`, error);
      return null;
    }
  }

  /**
   * Allocate dynamic port for ZAP container
   */
  private static async allocatePort(): Promise<number | null> {
    for (let port = this.ZAP_PORT_RANGE_START; port <= this.ZAP_PORT_RANGE_END; port++) {
      if (!this.usedPorts.has(port)) {
        // Check if port is actually available
        const isAvailable = await this.isPortAvailable(port);
        if (isAvailable) {
          this.usedPorts.add(port);
          console.log(`📍 Allocated port ${port} for ZAP container`);
          return port;
        }
      }
    }
    console.error(`❌ No available ports in range ${this.ZAP_PORT_RANGE_START}-${this.ZAP_PORT_RANGE_END}`);
    return null;
  }

  /**
   * Check if port is available
   */
  private static async isPortAvailable(port: number): Promise<boolean> {
    return new Promise((resolve) => {
      const net = require('net');
      const server = net.createServer();

      server.listen(port, () => {
        server.once('close', () => {
          resolve(true);
        });
        server.close();
      });

      server.on('error', () => {
        resolve(false);
      });
    });
  }

  /**
   * Wait for ZAP container to be ready
   */
  private static async waitForZapReady(containerInfo: ZapContainerInfo): Promise<void> {
    const maxRetries = 60; // 60 seconds timeout (ZAP can take time to start)
    const retryInterval = 2000; // 2 seconds between retries

    console.log(`⏳ Waiting for ZAP container to be ready: ${containerInfo.host}`);
    console.log(`🔍 Will check ZAP readiness every ${retryInterval/1000}s for up to ${maxRetries * retryInterval/1000}s`);

    for (let i = 0; i < maxRetries; i++) {
      try {
        console.log(`🔄 ZAP readiness check ${i + 1}/${maxRetries}: ${containerInfo.host}/JSON/core/view/version/`);

        const response = await axios.get(
          `${containerInfo.host}/JSON/core/view/version/?apikey=${this.ZAP_API_KEY}`,
          {
            timeout: 5000,
            validateStatus: (status) => status === 200
          }
        );

        if (response.status === 200 && response.data.version) {
          console.log(`✅ ZAP container ready: ${containerInfo.host} (version: ${response.data.version})`);
          console.log(`🕒 ZAP startup took ${(i + 1) * retryInterval/1000} seconds`);
          return;
        }
      } catch (error: any) {
        const errorMsg = error.code || error.message || 'Unknown error';
        console.log(`⏳ ZAP not ready yet (attempt ${i + 1}/${maxRetries}): ${errorMsg}`);

        // Log more details for debugging
        if (error.code === 'ECONNREFUSED') {
          console.log(`🔍 Connection refused - ZAP still starting up...`);
        } else if (error.code === 'ETIMEDOUT') {
          console.log(`🔍 Request timeout - ZAP may be slow to respond...`);
        }
      }

      // Don't wait after the last attempt
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, retryInterval));
      }
    }

    // Before failing, let's check if the container is still running
    console.log(`🔍 Final check: verifying container ${containerInfo.containerName} is still running...`);
    try {
      const { exec } = require('child_process');
      const checkCommand = `docker ps --filter "name=${containerInfo.containerName}" --format "{{.Status}}"`;

      const containerStatus = await new Promise<string>((resolve) => {
        exec(checkCommand, (_error: any, stdout: string) => {
          resolve(stdout.trim() || 'Not running');
        });
      });

      console.log(`📊 Container status: ${containerStatus}`);

      if (containerStatus.includes('Up')) {
        console.log(`⚠️ Container is running but ZAP API not responding after ${maxRetries * retryInterval/1000}s`);
      } else {
        console.log(`❌ Container stopped unexpectedly: ${containerStatus}`);
      }
    } catch (statusError) {
      console.log(`⚠️ Could not check container status: ${statusError}`);
    }

    throw new Error(`❌ ZAP container failed to start within ${maxRetries * retryInterval/1000} seconds: ${containerInfo.host}`);
  }

  /**
   * Release allocated port
   */
  private static releasePort(port: number): void {
    this.usedPorts.delete(port);
    console.log(`🔓 Released port ${port}`);
  }

  /**
   * Aggressively clear all ZAP data to prevent cross-contamination
   * @deprecated - Not needed with container isolation, each container is isolated
   */
  private static async clearAllZapData(): Promise<void> {
    console.log('⚠️ clearAllZapData is deprecated - using container isolation instead');
    // No-op: Container isolation makes this unnecessary
  }

  /**
   * Retry mechanism for ZAP API calls with exponential backoff
   */
  private static async retryZapApiCall<T>(
    operation: () => Promise<T>,
    operationName: string,
    maxRetries: number = 3,
    baseDelay: number = 2000
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 ${operationName} - Attempt ${attempt}/${maxRetries}`);
        return await operation();
      } catch (error: any) {
        lastError = error;

        // Check if this is a retryable error
        const isRetryable =
          error.response?.status === 504 || // Gateway Timeout
          error.response?.status === 502 || // Bad Gateway
          error.response?.status === 503 || // Service Unavailable
          error.code === 'ETIMEDOUT' ||     // Request timeout
          error.code === 'ECONNREFUSED' ||  // Connection refused
          error.code === 'ENOTFOUND';       // DNS resolution failed

        if (!isRetryable || attempt === maxRetries) {
          console.error(`❌ ${operationName} failed after ${attempt} attempts:`, error.message);
          throw error;
        }

        const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
        console.warn(`⚠️ ${operationName} failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`, error.message);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  /**
   * Get the direct pod IP for ZAP container to bypass service routing
   * This avoids the circular routing issue when calling ZAP API through the service
   */
  private static async getZapPodDirectUrl(clientId: string): Promise<string | null> {
    try {
      // Import Kubernetes client
      const k8s = require('@kubernetes/client-node');
      const kc = new k8s.KubeConfig();
      kc.loadFromDefault();
      const coreApi = kc.makeApiClient(k8s.CoreV1Api);

      // Convert clientId to DNS-compliant name
      const dnsClientId = clientId.toLowerCase().replace(/[_]/g, '-');
      const jobName = `zap-scan-${dnsClientId}`;
      const namespace = process.env.K8S_NAMESPACE || 'default';

      console.log(`🔍 Looking for pod with job label: ${jobName}`);

      // Find the pod for this job using Kubernetes API
      const pods = await coreApi.listNamespacedPod(
        namespace,
        undefined, // pretty
        undefined, // allowWatchBookmarks
        undefined, // continue
        undefined, // fieldSelector
        `job=${jobName}` // labelSelector
      );

      if (!pods.body.items || pods.body.items.length === 0) {
        console.error(`❌ No pod found for job ${jobName}`);
        return null;
      }

      const pod = pods.body.items[0];
      const podName = pod.metadata?.name;
      const podIP = pod.status?.podIP;

      if (!podIP) {
        console.error(`❌ Pod IP not found for pod ${podName}`);
        return null;
      }

      const directUrl = `http://${podIP}:8080`;
      console.log(`✅ Found ZAP pod direct URL: ${directUrl} (pod: ${podName})`);

      return directUrl;
    } catch (error) {
      console.error(`❌ Error getting ZAP pod direct URL:`, error);
      return null;
    }
  }

  /**
   * Get the appropriate ZAP API URL based on calling context
   * This avoids the circular routing issue when calling ZAP API
   */
  private static getZapApiUrl(containerInfo: ZapContainerInfo, endpoint: string): string {
    // For Kubernetes environments, we'll use kubectl exec instead of HTTP calls
    if (containerInfo.host.includes('.svc.cluster.local')) {
      // This will be handled by executeZapApiInPod
      return `${containerInfo.host}${endpoint}`;
    } else {
      // For Docker or direct access, use the provided host
      return `${containerInfo.host}${endpoint}`;
    }
  }

  /**
   * Verify that ZAP container has scan data available for report generation
   */
  private static async verifyContainerHasData(containerInfo: ZapContainerInfo): Promise<boolean> {
    try {
      let sitesData: any;

      // For Kubernetes environments, use direct pod IP to avoid circular routing
      if (containerInfo.host.includes('.svc.cluster.local')) {
        console.log(`🔧 Using direct pod IP to check ZAP scan data for CLIENT_ID ${containerInfo.clientId}`);

        // Get direct pod IP to bypass service routing
        const directUrl = await this.getZapPodDirectUrl(containerInfo.clientId);
        if (!directUrl) {
          throw new Error(`Failed to get direct pod URL for CLIENT_ID ${containerInfo.clientId}`);
        }

        const sitesUrl = `${directUrl}/JSON/core/view/sites/?apikey=${this.ZAP_API_KEY}`;
        console.log(`🔍 Calling ZAP API directly: ${sitesUrl}`);

        const sitesResponse = await this.retryZapApiCall(
          () => axios.get(sitesUrl, {
            timeout: 30000,
            validateStatus: (status) => status === 200
          }),
          `ZAP sites check (direct IP) for CLIENT_ID ${containerInfo.clientId}`,
          3,
          2000
        );

        sitesData = sitesResponse.data;
      } else {
        // For Docker environments, use HTTP calls
        const sitesUrl = this.getZapApiUrl(containerInfo, `/JSON/core/view/sites/?apikey=${this.ZAP_API_KEY}`);
        console.log(`🔍 Checking ZAP container scan data: ${sitesUrl}`);

        const sitesResponse = await this.retryZapApiCall(
          () => axios.get(sitesUrl, {
            timeout: 30000,
            validateStatus: (status) => status === 200
          }),
          `ZAP sites check for CLIENT_ID ${containerInfo.clientId}`,
          3,
          2000
        );

        sitesData = sitesResponse.data;
      }

      const sites = sitesData?.sites || [];

      console.log(`🔍 ZAP container scan data check for CLIENT_ID ${containerInfo.clientId}: ${sites.length} sites found`);

      if (sites.length > 0) {
        console.log(`✅ ZAP container has scan data for CLIENT_ID ${containerInfo.clientId}:`, sites);
        return true;
      } else {
        console.warn(`⚠️ ZAP container has no scan data for CLIENT_ID ${containerInfo.clientId}`);
        return false;
      }
    } catch (error: any) {
      console.error(`❌ Error checking ZAP container scan data for CLIENT_ID ${containerInfo.clientId}:`, error.message);

      // Log specific error details for debugging
      if (error.response) {
        console.error(`📤 HTTP ${error.response.status}: ${error.response.statusText}`);
      } else if (error.code === 'ECONNREFUSED') {
        console.error(`🔍 Connection refused - ZAP service may not be ready`);
      } else if (error.code === 'ETIMEDOUT') {
        console.error(`🔍 Request timeout - ZAP service is slow to respond`);
      } else if (error.code === 'ENOTFOUND') {
        console.error(`🔍 DNS resolution failed - Kubernetes service may not exist`);
      }

      return false;
    }
  }

  /**
   * Verify that ZAP has scan data available for report generation
   * @deprecated - Use verifyContainerHasData instead
   */
  private static async verifyZapHasData(sessionName: string, clientId: string): Promise<boolean> {
    console.log('⚠️ verifyZapHasData is deprecated - use verifyContainerHasData instead');
    return false;
  }

  /**
   * Verify that the ZAP session is properly isolated
   * @deprecated - Not needed with container isolation
   */
  private static async verifySessionIsolation(sessionName: string, clientId: string): Promise<void> {
    console.log('⚠️ verifySessionIsolation is deprecated - container isolation provides better isolation');
    // No-op: Container isolation makes this unnecessary
  }

  /**
   * Clear stored reports for a test case to prevent contaminated data
   */
  private static async clearStoredReports(testCaseId: string): Promise<void> {
    try {
      console.log(`🧹 Clearing stored reports for test case: ${testCaseId} to prevent contamination`);

      // Call backend API to clear stored security reports for this test case
      const response = await axios.delete(
        `${this.BACKEND_URL}/temp-test-results/security-reports/${testCaseId}`,
        { timeout: 5000 }
      );

      if (response.status === 200) {
        console.log(`✅ Stored reports cleared for test case: ${testCaseId}`);
      } else {
        console.warn(`⚠️ Failed to clear stored reports for test case: ${testCaseId}`);
      }
    } catch (error) {
      console.warn(`⚠️ Could not clear stored reports for test case ${testCaseId}:`, (error as Error).message);
      // Don't throw error - continue with fresh report generation
    }
  }

  /**
   * Clean up ZAP session for a client
   * @deprecated - Use removeClientContainer instead
   */
  private static async cleanupClientSession(clientId: string): Promise<void> {
    console.log('⚠️ cleanupClientSession is deprecated - use removeClientContainer instead');
    // No-op: Container-based cleanup is handled by removeClientContainer
  }

  /**
   * Generate ZAP security report for a completed test using dedicated container
   * @param clientId - Unique client ID for isolation
   * @param testCaseId - Test case ID
   * @returns Promise<ZapReportData | null>
   */
  static async generateSecurityReport(clientId: string, testCaseId: string): Promise<ZapReportData | null> {
    // Queue this ZAP operation to prevent concurrent scan interference
    return this.queueZapOperation(async () => {
      try {
        console.log(`🔍 Generating ZAP security report for client: ${clientId}, test case: ${testCaseId} (queued)`);

        // Get the dedicated ZAP container for this client
        const containerInfo = this.activeContainers.get(clientId);
        if (!containerInfo) {
          console.error(`❌ No ZAP container found for CLIENT_ID: ${clientId}`);
          return null;
        }

        console.log(`📊 Fetching ZAP scan data from dedicated container: ${containerInfo.host}`);

        // Verify that ZAP container has scan data before proceeding
        const zapHealthCheck = await this.verifyContainerHasData(containerInfo);
        if (!zapHealthCheck) {
          console.warn(`⚠️ No ZAP scan data found in container for CLIENT_ID: ${clientId}`);
          console.log(`📝 This is normal if the test didn't make any HTTP requests through the ZAP proxy`);

          // Generate an empty security report indicating no scan data
          const emptyReport: ZapReportData = {
            clientId: clientId,
            testCaseId: testCaseId,
            timestamp: new Date().toISOString(),
            html: '<html><body><h1>ZAP Security Report</h1><p>No HTTP traffic was captured through the ZAP proxy during test execution.</p><p>Status: No scan data available</p></body></html>',
            json: {
              scanStatus: 'no_data',
              message: 'No HTTP traffic was captured through the ZAP proxy during test execution',
              sites: [],
              alerts: []
            },
            vulnerabilities: [],
            summary: {
              totalIssues: 0,
              highRisk: 0,
              mediumRisk: 0,
              lowRisk: 0,
              informational: 0,
              scanDuration: 0,
              urlsScanned: 0
            },
            scanContext: {
              sites: [],
              urls: [],
              hosts: [],
              targetUrl: undefined,
              scanStartTime: new Date().toISOString(),
              scanEndTime: new Date().toISOString()
            }
          };

          console.log(`📄 Generated empty security report for CLIENT_ID: ${clientId} (no scan data)`);
          return emptyReport;
        }

      // Get HTML report from ZAP container
      const htmlReport = await this.fetchZapHtmlReportFromContainer(containerInfo);
      if (!htmlReport) {
        console.warn('⚠️ No ZAP HTML report available from container');
        return null;
      }

      // Get JSON report from ZAP container for detailed analysis
      const jsonReport = await this.fetchZapJsonReportFromContainer(containerInfo);

      // Get scan context information from ZAP container
      console.log('🔍 Fetching ZAP scan context from container...');
      const scanContext = await this.fetchZapScanContextFromContainer(containerInfo);
      console.log(`✅ Scan context fetched: ${scanContext.sites.length} sites, ${scanContext.urls.length} URLs`);

      // Parse vulnerabilities from the reports
      const vulnerabilities = this.parseVulnerabilities(htmlReport, jsonReport);
      const summary = this.generateSummary(vulnerabilities, scanContext);

      const reportData: ZapReportData = {
        html: htmlReport,
        json: jsonReport,
        timestamp: new Date().toISOString(),
        testCaseId,
        clientId,
        vulnerabilities,
        summary,
        scanContext
      };

      // Save report to client-isolated directory
      const reportPath = await this.saveReportToFile(reportData);
      console.log(`📊 ZAP report saved to: ${reportPath}`);

      // CRITICAL: Clear any old stored reports BEFORE storing fresh report
      await this.clearStoredReports(testCaseId);

      // Note: Database storage is now handled by test-runner.ts using the proper test-results endpoint
      // This ensures the logsSecurityUrl is stored in the correct test result record

      // Note: Container cleanup is now handled by test-runner.ts after test completion
      // This ensures containers are cleaned up regardless of report generation success

        return reportData;
      } catch (error) {
        console.error('❌ Error generating ZAP security report:', error);
        // Note: Container cleanup is handled by test-runner.ts
        return null;
      }
    });
  }

  /**
   * Fetch HTML report from ZAP container
   */
  private static async fetchZapHtmlReportFromContainer(containerInfo: ZapContainerInfo): Promise<string | null> {
    try {
      if (containerInfo.host.includes('.svc.cluster.local')) {
        // Use direct pod IP for Kubernetes environments
        console.log(`📄 Fetching HTML report using direct pod IP for ${containerInfo.containerName}`);

        // Get direct pod IP to bypass service routing
        const directUrl = await this.getZapPodDirectUrl(containerInfo.clientId);
        if (!directUrl) {
          throw new Error(`Failed to get direct pod URL for CLIENT_ID ${containerInfo.clientId}`);
        }

        const htmlUrl = `${directUrl}/OTHER/core/other/htmlreport/?apikey=${this.ZAP_API_KEY}`;
        console.log(`🔍 Calling ZAP HTML report API directly: ${htmlUrl}`);

        const response = await this.retryZapApiCall(
          () => axios.get(htmlUrl, { timeout: 30000 }),
          `ZAP HTML report fetch (direct IP) for ${containerInfo.containerName}`,
          3,
          2000
        );
        return response.data;
      } else {
        // Use HTTP for Docker environments
        const url = `${containerInfo.host}/OTHER/core/other/htmlreport/?apikey=${this.ZAP_API_KEY}`;
        console.log(`📄 Fetching HTML report from container: ${url}`);

        const response = await this.retryZapApiCall(
          () => axios.get(url, { timeout: 30000 }),
          `ZAP HTML report fetch for ${containerInfo.containerName}`,
          3,
          2000
        );
        return response.data;
      }
    } catch (error) {
      console.error(`❌ Error fetching HTML report from container ${containerInfo.containerName}:`, (error as Error).message);
      return null;
    }
  }

  /**
   * Fetch JSON report from ZAP container for detailed analysis
   */
  private static async fetchZapJsonReportFromContainer(containerInfo: ZapContainerInfo): Promise<any | null> {
    try {
      if (containerInfo.host.includes('.svc.cluster.local')) {
        // Use direct pod IP for Kubernetes environments
        console.log(`📊 Fetching JSON report using direct pod IP for ${containerInfo.containerName}`);

        // Get direct pod IP to bypass service routing
        const directUrl = await this.getZapPodDirectUrl(containerInfo.clientId);
        if (!directUrl) {
          throw new Error(`Failed to get direct pod URL for CLIENT_ID ${containerInfo.clientId}`);
        }

        const alertsUrl = `${directUrl}/JSON/core/view/alerts/?apikey=${this.ZAP_API_KEY}`;
        console.log(`🔍 Calling ZAP alerts API directly: ${alertsUrl}`);

        const alertsResponse = await this.retryZapApiCall(
          () => axios.get(alertsUrl, { timeout: 30000 }),
          `ZAP JSON report fetch (direct IP) for ${containerInfo.containerName}`,
          3,
          2000
        );
        return alertsResponse.data;
      } else {
        // Use HTTP for Docker environments
        const url = `${containerInfo.host}/JSON/core/view/alerts/?apikey=${this.ZAP_API_KEY}`;
        console.log(`📊 Fetching JSON report from container: ${url}`);

        const response = await this.retryZapApiCall(
          () => axios.get(url, { timeout: 30000 }),
          `ZAP JSON report fetch for ${containerInfo.containerName}`,
          3,
          2000
        );
        return response.data;
      }
    } catch (error) {
      console.error(`❌ Error fetching JSON report from container ${containerInfo.containerName}:`, (error as Error).message);
      return null;
    }
  }

  /**
   * Fetch scan context information from ZAP container
   */
  private static async fetchZapScanContextFromContainer(containerInfo: ZapContainerInfo): Promise<any> {
    const scanContext: any = {
      sites: [],
      urls: [],
      hosts: [],
      scanStartTime: new Date().toISOString(),
      scanEndTime: new Date().toISOString(),
      targetUrl: null
    };

    try {
      // Fetch sites that were accessed with retry mechanism
      let sitesData: any;

      if (containerInfo.host.includes('.svc.cluster.local')) {
        // Use direct pod IP for Kubernetes environments
        const directUrl = await this.getZapPodDirectUrl(containerInfo.clientId);
        if (directUrl) {
          const sitesUrl = `${directUrl}/JSON/core/view/sites/?apikey=${this.ZAP_API_KEY}`;
          const sitesResponse = await this.retryZapApiCall(
            () => axios.get(sitesUrl, { timeout: 30000 }),
            `ZAP sites fetch (direct IP) for ${containerInfo.containerName}`,
            3,
            2000
          );
          sitesData = sitesResponse.data;
        } else {
          console.warn(`⚠️ Could not get direct pod URL, skipping sites fetch`);
          sitesData = { sites: [] };
        }
      } else {
        // Use HTTP for Docker environments
        const sitesResponse = await this.retryZapApiCall(
          () => axios.get(
            `${containerInfo.host}/JSON/core/view/sites/?apikey=${this.ZAP_API_KEY}`,
            { timeout: 30000 }
          ),
          `ZAP sites fetch for ${containerInfo.containerName}`,
          3,
          2000
        );
        sitesData = sitesResponse.data;
      }

      if (sitesData && sitesData.sites) {
        scanContext.sites = sitesData.sites;
      }
    } catch (error) {
      console.warn(`⚠️ Error fetching ZAP sites from container ${containerInfo.containerName}:`, (error as Error).message);
    }

    try {
      // Fetch URLs that were accessed with retry mechanism
      let urlsData: any;

      if (containerInfo.host.includes('.svc.cluster.local')) {
        // Use direct pod IP for Kubernetes environments
        const directUrl = await this.getZapPodDirectUrl(containerInfo.clientId);
        if (directUrl) {
          const urlsUrl = `${directUrl}/JSON/core/view/urls/?apikey=${this.ZAP_API_KEY}`;
          const urlsResponse = await this.retryZapApiCall(
            () => axios.get(urlsUrl, { timeout: 30000 }),
            `ZAP URLs fetch (direct IP) for ${containerInfo.containerName}`,
            3,
            2000
          );
          urlsData = urlsResponse.data;
        } else {
          console.warn(`⚠️ Could not get direct pod URL, skipping URLs fetch`);
          urlsData = { urls: [] };
        }
      } else {
        // Use HTTP for Docker environments
        const urlsResponse = await this.retryZapApiCall(
          () => axios.get(
            `${containerInfo.host}/JSON/core/view/urls/?apikey=${this.ZAP_API_KEY}`,
            { timeout: 30000 }
          ),
          `ZAP URLs fetch for ${containerInfo.containerName}`,
          3,
          2000
        );
        urlsData = urlsResponse.data;
      }

      if (urlsData && urlsData.urls) {
        scanContext.urls = urlsData.urls;
      }
    } catch (error) {
      console.warn(`⚠️ Error fetching ZAP URLs from container ${containerInfo.containerName}:`, (error as Error).message);
    }

    try {
      // Fetch hosts that were accessed with retry mechanism
      let hostsData: any;

      if (containerInfo.host.includes('.svc.cluster.local')) {
        // Use direct pod IP for Kubernetes environments
        const directUrl = await this.getZapPodDirectUrl(containerInfo.clientId);
        if (directUrl) {
          const hostsUrl = `${directUrl}/JSON/core/view/hosts/?apikey=${this.ZAP_API_KEY}`;
          const hostsResponse = await this.retryZapApiCall(
            () => axios.get(hostsUrl, { timeout: 30000 }),
            `ZAP hosts fetch (direct IP) for ${containerInfo.containerName}`,
            3,
            2000
          );
          hostsData = hostsResponse.data;
        } else {
          console.warn(`⚠️ Could not get direct pod URL, skipping hosts fetch`);
          hostsData = { hosts: [] };
        }
      } else {
        // Use HTTP for Docker environments
        const hostsResponse = await this.retryZapApiCall(
          () => axios.get(
            `${containerInfo.host}/JSON/core/view/hosts/?apikey=${this.ZAP_API_KEY}`,
            { timeout: 30000 }
          ),
          `ZAP hosts fetch for ${containerInfo.containerName}`,
          3,
          2000
        );
        hostsData = hostsResponse.data;
      }

      if (hostsData && hostsData.hosts) {
        scanContext.hosts = hostsData.hosts;
      }
    } catch (error) {
      console.warn(`⚠️ Error fetching ZAP hosts from container ${containerInfo.containerName}:`, (error as Error).message);
    }

    // Set target URL from the first site if available
    if (scanContext.sites.length > 0) {
      scanContext.targetUrl = scanContext.sites[0];
    }

    console.log(`📊 ZAP container scan context: ${scanContext.sites.length} sites, ${scanContext.urls.length} URLs, ${scanContext.hosts.length} hosts`);

    return scanContext;
  }

  /**
   * Fetch HTML report from ZAP (session-isolated)
   * @deprecated - Use fetchZapHtmlReportFromContainer instead
   */
  private static async fetchZapHtmlReport(_sessionName?: string): Promise<string | null> {
    console.log('⚠️ fetchZapHtmlReport is deprecated - use fetchZapHtmlReportFromContainer instead');
    return null;
  }

  /**
   * Fetch JSON report from ZAP for detailed analysis (session-isolated)
   * @deprecated - Use fetchZapJsonReportFromContainer instead
   */
  private static async fetchZapJsonReport(_sessionName?: string): Promise<any | null> {
    console.log('⚠️ fetchZapJsonReport is deprecated - use fetchZapJsonReportFromContainer instead');
    return null;
  }

  /**
   * Fetch scan context information from ZAP (session-isolated)
   * @deprecated - Use fetchZapScanContextFromContainer instead
   */
  private static async fetchZapScanContext(_sessionName?: string): Promise<ZapScanContext> {
    console.log('⚠️ fetchZapScanContext is deprecated - use fetchZapScanContextFromContainer instead');
    return {
      sites: [],
      urls: [],
      hosts: [],
      scanStartTime: new Date().toISOString(),
      scanEndTime: new Date().toISOString()
    };
  }

  /**
   * @deprecated - Legacy method body removed
   */
  private static async fetchZapScanContextLegacy(_sessionName?: string): Promise<ZapScanContext> {
    console.log('⚠️ fetchZapScanContextLegacy is deprecated - legacy method body removed');
    return {
      sites: [],
      urls: [],
      hosts: [],
      scanStartTime: new Date().toISOString(),
      scanEndTime: new Date().toISOString()
    };
  }

  /**
   * Parse vulnerabilities from ZAP reports
   */
  private static parseVulnerabilities(htmlReport: string, jsonReport?: any): ZapVulnerability[] {
    const vulnerabilities: ZapVulnerability[] = [];

    try {
      // Parse from JSON report if available (more structured)
      if (jsonReport && jsonReport.alerts) {
        jsonReport.alerts.forEach((alert: any) => {
          const vulnerability: ZapVulnerability = {
            name: alert.name || 'Unknown Vulnerability',
            risk: this.mapRiskLevel(alert.risk),
            confidence: alert.confidence || 'Unknown',
            description: alert.description || '',
            solution: alert.solution || '',
            reference: alert.reference || '',
            instances: alert.instances?.map((instance: any) => ({
              uri: instance.uri || '',
              method: instance.method || '',
              param: instance.param || '',
              evidence: instance.evidence || ''
            })) || []
          };
          vulnerabilities.push(vulnerability);
        });
      } else {
        // Fallback to HTML parsing if JSON not available
        const htmlVulns = this.parseHtmlReport(htmlReport);
        vulnerabilities.push(...htmlVulns);
      }
    } catch (error) {
      console.error('Error parsing vulnerabilities:', error);
    }

    return vulnerabilities;
  }

  /**
   * Parse vulnerabilities from HTML report (fallback method)
   */
  private static parseHtmlReport(htmlReport: string): ZapVulnerability[] {
    const vulnerabilities: ZapVulnerability[] = [];

    try {
      // Simple regex-based parsing for HTML report
      const vulnRegex = /<h3[^>]*>([^<]+)<\/h3>[\s\S]*?Risk:\s*([^<\n]+)/gi;
      let match;

      while ((match = vulnRegex.exec(htmlReport)) !== null) {
        const name = match[1].trim();
        const risk = this.mapRiskLevel(match[2].trim());

        vulnerabilities.push({
          name,
          risk,
          confidence: 'Unknown',
          description: `Security vulnerability: ${name}`,
          instances: []
        });
      }
    } catch (error) {
      console.error('Error parsing HTML report:', error);
    }

    return vulnerabilities;
  }

  /**
   * Map ZAP risk levels to standardized format
   */
  private static mapRiskLevel(risk: string): 'High' | 'Medium' | 'Low' | 'Informational' {
    const riskLower = risk.toLowerCase();
    if (riskLower.includes('high')) return 'High';
    if (riskLower.includes('medium')) return 'Medium';
    if (riskLower.includes('low')) return 'Low';
    return 'Informational';
  }

  /**
   * Generate summary statistics from vulnerabilities
   */
  private static generateSummary(vulnerabilities: ZapVulnerability[], scanContext?: ZapScanContext): ZapSummary {
    const summary: ZapSummary = {
      totalIssues: vulnerabilities.length,
      highRisk: vulnerabilities.filter(v => v.risk === 'High').length,
      mediumRisk: vulnerabilities.filter(v => v.risk === 'Medium').length,
      lowRisk: vulnerabilities.filter(v => v.risk === 'Low').length,
      informational: vulnerabilities.filter(v => v.risk === 'Informational').length,
      urlsScanned: scanContext?.urls.length || 0
    };

    return summary;
  }

  /**
   * Save ZAP report to client-isolated file system
   */
  private static async saveReportToFile(reportData: ZapReportData): Promise<string> {
    try {
      // CRITICAL: Use CLIENT_ID from reportData to ensure proper isolation
      // This prevents cross-contamination between different users/companies in SaaS
      const clientId = reportData.clientId || process.env.CLIENT_ID || 'default';
      const outputDir = `test-results/${clientId}`;

      const zapReportsDir = path.join(outputDir, 'zap-reports');

      // Ensure directory exists
      if (!fs.existsSync(zapReportsDir)) {
        fs.mkdirSync(zapReportsDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const reportFileName = `zap-report-${reportData.testCaseId}-${timestamp}.json`;
      const reportPath = path.join(zapReportsDir, reportFileName);

      // Save complete report data as JSON
      fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));

      // Also save HTML report separately for easy viewing
      const htmlFileName = `zap-report-${reportData.testCaseId}-${timestamp}.html`;
      const htmlPath = path.join(zapReportsDir, htmlFileName);
      fs.writeFileSync(htmlPath, reportData.html);

      console.log(`📁 ZAP report saved to CLIENT_ID isolated path: ${reportPath}`);
      console.log(`🔒 CLIENT_ID: ${clientId} - Report isolation ensured`);
      return reportPath;
    } catch (error) {
      console.error('Error saving ZAP report to file:', error);
      throw error;
    }
  }



  /**
   * Initialize dedicated ZAP resource for CLIENT_ID before starting new test
   * This ensures complete isolation between different users/companies
   * Uses Kubernetes Jobs by default, falls back to Docker containers if configured
   */
  static async initializeClientContainer(clientId: string): Promise<ZapContainerInfo | null> {
    // Queue this ZAP operation to prevent concurrent resource creation
    return this.queueZapOperation(async () => {
      try {
        if (this.USE_KUBERNETES) {
          console.log(`🔧 Initializing ZAP Job for CLIENT_ID: ${clientId}... (queued)`);

          // Create dedicated ZAP Job for this client
          const jobInfo = await KubernetesService.createZapJob(clientId);

          if (jobInfo) {
            // Convert ZapJobInfo to ZapContainerInfo for backward compatibility
            const containerInfo: ZapContainerInfo = {
              containerId: jobInfo.jobName,
              containerName: jobInfo.serviceName,
              port: jobInfo.port,
              host: jobInfo.host,
              clientId: jobInfo.clientId,
              createdAt: jobInfo.createdAt
            };

            // Wait for ZAP container to be ready
            await this.waitForZapReady(containerInfo);

            // Track the job
            this.activeJobs.set(clientId, jobInfo);
            this.activeContainers.set(clientId, containerInfo); // For backward compatibility

            console.log(`✅ ZAP Job ready for CLIENT_ID: ${clientId} at ${jobInfo.host}`);
            return containerInfo;
          } else {
            console.error(`❌ Failed to create ZAP Job for CLIENT_ID: ${clientId}`);
            return null;
          }
        } else {
          console.log(`🔧 Initializing ZAP container for CLIENT_ID: ${clientId}... (queued)`);

          // Create dedicated ZAP container for this client (legacy Docker mode)
          const containerInfo = await this.createZapContainer(clientId);

          if (containerInfo) {
            console.log(`✅ ZAP container initialized for CLIENT_ID: ${clientId} on port ${containerInfo.port}`);
            return containerInfo;
          } else {
            console.error(`❌ Failed to create ZAP container for CLIENT_ID: ${clientId}`);
            return null;
          }
        }
      } catch (error) {
        console.error(`❌ Failed to initialize ZAP resource for CLIENT_ID ${clientId}:`, error);
        return null;
      }
    });
  }

  /**
   * Get ZAP container info for CLIENT_ID
   */
  static getClientContainer(clientId: string): ZapContainerInfo | null {
    return this.activeContainers.get(clientId) || null;
  }

  /**
   * Remove ZAP resource for CLIENT_ID with improved reliability
   * Handles both Kubernetes Jobs and Docker containers
   */
  static async removeClientContainer(clientId: string): Promise<void> {
    console.log(`🔍 removeClientContainer called for CLIENT_ID: ${clientId}`);
    console.log(`📊 Active resources: Jobs=${Array.from(this.activeJobs.keys()).join(', ')}, Containers=${Array.from(this.activeContainers.keys()).join(', ')}`);

    if (this.USE_KUBERNETES) {
      // Handle Kubernetes Job cleanup
      try {
        console.log(`🗑️ Removing ZAP Job for CLIENT_ID: ${clientId}`);
        await KubernetesService.deleteZapJob(clientId);

        // Clean up tracking data
        this.activeJobs.delete(clientId);
        this.activeContainers.delete(clientId); // Remove backward compatibility entry

        console.log(`🧹 ZAP Job cleanup completed for CLIENT_ID: ${clientId}`);
      } catch (error) {
        console.error(`❌ Error during ZAP Job cleanup for CLIENT_ID ${clientId}:`, error);
      }
    } else {
      // Handle Docker container cleanup (legacy mode)
      await this.removeDockerContainer(clientId);
    }
  }

  /**
   * Remove Docker container for CLIENT_ID (legacy support)
   */
  private static async removeDockerContainer(clientId: string): Promise<void> {
    const containerInfo = this.activeContainers.get(clientId);
    const { exec } = require('child_process');

    // Always try to find and remove containers by pattern (more reliable)
    const findCommand = `docker ps -a --filter "name=zap-scan-${clientId}" --format "{{.Names}}"`;

    try {
      const containerNames = await new Promise<string[]>((resolve) => {
        exec(findCommand, (error: any, stdout: string, _stderr: string) => {
          if (error) {
            console.log(`📝 No containers found for CLIENT_ID: ${clientId}`);
            resolve([]);
          } else {
            const names = stdout.trim().split('\n').filter(name => name.length > 0);
            console.log(`🔍 Found containers for CLIENT_ID ${clientId}:`, names);
            resolve(names);
          }
        });
      });

      // Remove all found containers
      for (const containerName of containerNames) {
        try {
          console.log(`🗑️ Removing container: ${containerName}`);
          const removeCommand = `docker rm -f ${containerName}`;

          await new Promise<void>((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('Container removal timeout after 10 seconds'));
            }, 10000);

            exec(removeCommand, (error: any, _stdout: string, stderr: string) => {
              clearTimeout(timeout);
              if (error) {
                console.warn(`⚠️ Error removing container ${containerName}:`, error.message);
                if (stderr) console.warn(`⚠️ stderr:`, stderr);
                // Don't reject - container might already be removed or in use
              } else {
                console.log(`✅ ZAP container removed: ${containerName}`);
              }
              resolve();
            });
          });
        } catch (removeError) {
          console.warn(`⚠️ Failed to remove container ${containerName}:`, removeError);
          // Continue with other containers
        }
      }

      // Clean up tracking data if we had container info
      if (containerInfo) {
        // Release the port
        this.releasePort(containerInfo.port);
        console.log(`🔓 Released port ${containerInfo.port} for CLIENT_ID: ${clientId}`);

        // Remove from active containers
        this.activeContainers.delete(clientId);
        console.log(`🗑️ Removed CLIENT_ID: ${clientId} from active containers map`);
      }

      console.log(`🧹 ZAP container cleanup completed for CLIENT_ID: ${clientId}`);

      // Verify cleanup was successful
      setTimeout(() => this.verifyContainerCleanup(clientId), 2000);

    } catch (error) {
      console.error(`❌ Error during container cleanup for CLIENT_ID ${clientId}:`, error);

      // Force cleanup attempt as last resort
      try {
        console.log(`🚨 Attempting force cleanup for CLIENT_ID: ${clientId}`);
        const forceCommand = `docker ps -a | grep zap-scan-${clientId} | awk '{print $1}' | xargs -r docker rm -f`;
        exec(forceCommand, (error: any, _stdout: string, _stderr: string) => {
          if (error) {
            console.warn(`⚠️ Force cleanup failed for CLIENT_ID ${clientId}:`, error.message);
          } else {
            console.log(`🚨 Force cleanup completed for CLIENT_ID: ${clientId}`);
          }
        });
      } catch (forceError) {
        console.error(`❌ Force cleanup also failed for CLIENT_ID ${clientId}:`, forceError);
      }
    }
  }

  /**
   * Verify that container cleanup was successful
   */
  private static async verifyContainerCleanup(clientId: string): Promise<void> {
    const { exec } = require('child_process');
    const checkCommand = `docker ps -a --filter "name=zap-scan-${clientId}" --format "{{.Names}}"`;

    exec(checkCommand, (error: any, stdout: string, _stderr: string) => {
      if (error) {
        console.log(`✅ Container cleanup verification passed for CLIENT_ID: ${clientId}`);
      } else {
        const remainingContainers = stdout.trim().split('\n').filter(name => name.length > 0);
        if (remainingContainers.length > 0) {
          console.warn(`⚠️ Container cleanup verification failed for CLIENT_ID: ${clientId}. Remaining containers:`, remainingContainers);
          // Schedule another cleanup attempt
          setTimeout(() => this.removeClientContainer(clientId), 5000);
        } else {
          console.log(`✅ Container cleanup verification passed for CLIENT_ID: ${clientId}`);
        }
      }
    });
  }

  /**
   * Clear ZAP session before starting new test (legacy method - deprecated)
   * @deprecated Use initializeClientContainer instead for proper CLIENT_ID isolation
   */
  static async clearZapSession(): Promise<void> {
    console.log('⚠️ clearZapSession is deprecated - use initializeClientContainer instead');
    // No-op: Container-based isolation makes this unnecessary
  }
}
