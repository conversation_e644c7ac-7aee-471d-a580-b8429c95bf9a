import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TestResult } from './test-result.entity';
import { Storage } from '@google-cloud/storage';

@Injectable()
export class TestResultsService {
  private storage: Storage;
  private bucketName: string;

  constructor(
    @InjectRepository(TestResult)
    private testResultsRepository: Repository<TestResult>,
  ) {
    // Initialize Google Cloud Storage
    this.storage = new Storage({
      projectId: process.env.GCP_PROJECT_ID,
      credentials: {
        client_email: process.env.GCP_CLIENT_EMAIL,
        private_key: process.env.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
    });
    this.bucketName = process.env.GCP_BUCKET_NAME || 'agentq-test-logs';
  }

  /**
   * Create a new test result (simple, no project/testRun required)
   */
  async createSimpleTestResult(
    createDto: { id?: string; testRunId: string; testCaseId: string; status?: string; createdBy?: string }
  ): Promise<{ success: boolean; id: string }> {
    // Import the TestResultStatus enum
    const statusEnum = (this.testResultsRepository.metadata.columns.find(col => col.propertyName === 'status')?.enum || ['untested'])[0];
    const entity = this.testResultsRepository.create({
      testRunId: createDto.testRunId,
      testCaseId: createDto.testCaseId,
      status: (createDto.status as any) || statusEnum || 'untested',
      createdBy: createDto.createdBy || null,
    });
    await this.testResultsRepository.save(entity);
    return { success: true, id: entity.id };
  }

  /**
   * Patch videoUrl, screenshotUrl, and/or logsSecurityUrl for a test result by testResultId
   */
  async patchTestResult(
    testResultId: string,
    updateDto: { videoUrl?: string; screenshotUrl?: string; logsSecurityUrl?: string; createdBy?: string }
  ): Promise<{ success: boolean }> {
    const updateFields: any = {};
    if (updateDto.videoUrl !== undefined) updateFields.videoUrl = updateDto.videoUrl;
    if (updateDto.screenshotUrl !== undefined) updateFields.screenshotUrl = updateDto.screenshotUrl;
    if (updateDto.logsSecurityUrl !== undefined) updateFields.logsSecurityUrl = updateDto.logsSecurityUrl;
    if (updateDto.createdBy !== undefined) updateFields.createdBy = updateDto.createdBy;

    if (Object.keys(updateFields).length === 0) {
      throw new Error('No fields to update');
    }

    try {
      const result = await this.testResultsRepository.update(
        { id: testResultId },
        updateFields
      );

      if (result.affected && result.affected > 0) {
        return { success: true };
      } else {
        console.error(`patchTestResult: No test result found for id=${testResultId}`);
        throw new Error('Test result not found or not updated');
      }
    } catch (err) {
      console.error(`patchTestResult: Error updating testResultId=${testResultId}`, err);
      throw err;
    }
  }

  /**
   * Update test result with video URL
   */
  async updateTestResultVideoUrl(resultId: string, videoUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { videoUrl });
  }

  /**
   * Update test result with screenshot URL
   */
  async updateTestResultScreenshotUrl(resultId: string, screenshotUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { screenshotUrl });
  }

  /**
   * Update test result with logs URL
   */
  async updateTestResultLogsUrl(resultId: string, logsUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { logsUrl });
  }

  /**
   * Update test result with security logs URL (DAST)
   */
  async updateTestResultSecurityLogsUrl(resultId: string, logsSecurityUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { logsSecurityUrl });
  }

  /**
   * Update test result with vulnerability description (DAST)
   */
  async updateTestResultVulnerabilityDescription(resultId: string, vulnerabilityDescription: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { vulnerabilityDescription });
  }

  /**
   * Update additional DAST fields from ZAP report
   */
  async updateAdditionalDastFields(testResultId: string, zapReport: any): Promise<void> {
    try {
      const updateData: any = {};

      // Extract affected URLs from scan context
      if (zapReport.scanContext?.sites) {
        const affectedUrls = zapReport.scanContext.sites.slice(0, 20).join('\n'); // Limit to first 20 URLs
        updateData.affectedUrls = affectedUrls;
      }

      // Determine original severity based on highest risk level found
      if (zapReport.summary) {
        const { highRisk, mediumRisk, lowRisk, informational } = zapReport.summary;
        if (highRisk > 0) {
          updateData.originalSeverity = 'High';
        } else if (mediumRisk > 0) {
          updateData.originalSeverity = 'Medium';
        } else if (lowRisk > 0) {
          updateData.originalSeverity = 'Low';
        } else if (informational > 0) {
          updateData.originalSeverity = 'Informational';
        } else {
          updateData.originalSeverity = 'None';
        }
      }

      // Update the test result with additional DAST data
      if (Object.keys(updateData).length > 0) {
        await this.testResultsRepository.update(testResultId, updateData);
        console.log(`✅ Additional DAST fields updated for test result: ${testResultId}`, updateData);
      }
    } catch (error) {
      console.error('Error updating additional DAST fields:', error);
    }
  }

  /**
   * Store ZAP security report for a test result
   */
  async storeSecurityReport(
    testResultId: string,
    zapReport: any,
    clientId?: string
  ): Promise<{ success: boolean; logsSecurityUrl: string }> {
    try {
      // Extract CLIENT_ID from zapReport if not provided
      const effectiveClientId = clientId || zapReport?.clientId || 'default';

      // Upload security logs to Google Cloud Storage with CLIENT_ID isolation
      const fileName = `test-results/logs-dast/${effectiveClientId}/${testResultId}/zap-report-${Date.now()}.json`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      // Convert zapReport to JSON string if it's an object
      const reportData = typeof zapReport === 'string' ? zapReport : JSON.stringify(zapReport, null, 2);

      await file.save(reportData, {
        metadata: {
          contentType: 'application/json',
          clientId: effectiveClientId, // Add CLIENT_ID to metadata for tracking
        },
      });

      const logsSecurityUrl = `gs://${this.bucketName}/${fileName}`;

      // Update the test result with security logs URL
      await this.updateTestResultSecurityLogsUrl(testResultId, logsSecurityUrl);

      // Debug: Log the ZAP report structure
      console.log(`🔍 Debug: ZAP report structure for ${testResultId}:`, {
        hasReport: !!zapReport,
        hasSummary: !!zapReport?.summary,
        hasScanContext: !!zapReport?.scanContext,
        summaryKeys: zapReport?.summary ? Object.keys(zapReport.summary) : [],
        scanContextKeys: zapReport?.scanContext ? Object.keys(zapReport.scanContext) : [],
        reportKeys: zapReport ? Object.keys(zapReport) : []
      });

      // Generate vulnerability description from DAST summary and scan context
      const vulnerabilityDescription = this.generateVulnerabilityDescription(zapReport);
      console.log(`🔍 Debug: Generated vulnerability description length: ${vulnerabilityDescription?.length || 0}`);

      if (vulnerabilityDescription) {
        await this.updateTestResultVulnerabilityDescription(testResultId, vulnerabilityDescription);
        console.log(`✅ Vulnerability description updated for test result: ${testResultId}`);
      } else {
        console.warn(`⚠️ No vulnerability description generated for test result: ${testResultId}`);
      }

      // Extract and update additional DAST fields
      await this.updateAdditionalDastFields(testResultId, zapReport);

      console.log(`Security logs uploaded successfully to GCS for test result: ${testResultId} (CLIENT_ID: ${effectiveClientId})`);
      console.log(`🔒 CLIENT_ID isolation ensured in path: ${fileName}`);

      return { success: true, logsSecurityUrl };
    } catch (error) {
      console.error(`Failed to store security report for test result ${testResultId}:`, error);
      throw error;
    }
  }

  /**
   * Find a test result by ID, projectId, and testRunId
   */
  async findByIdAndProjectAndTestRun(resultId: string, projectId: string, testRunId: string): Promise<TestResult | undefined> {
    return this.testResultsRepository
      .createQueryBuilder('testResult')
      .leftJoinAndSelect('testResult.testRun', 'testRun')
      .leftJoinAndSelect('testResult.testCase', 'testCase')
      .where('testResult.id = :resultId', { resultId })
      .andWhere('testRun.id = :testRunId', { testRunId })
      .andWhere('testRun.projectId = :projectId', { projectId })
      .getOne();
  }

  /**
   * Find a test result by ID only (for DAST integration)
   */
  async findById(resultId: string): Promise<TestResult | undefined> {
    return this.testResultsRepository.findOne({
      where: { id: resultId },
      relations: ['testRun', 'testCase']
    });
  }

  /**
   * Generate vulnerability description from ZAP report summary and scan context
   */
  private generateVulnerabilityDescription(zapReport: any): string | null {
    try {
      console.log(`🔍 Debug: generateVulnerabilityDescription called with:`, {
        hasReport: !!zapReport,
        hasSummary: !!zapReport?.summary,
        summaryContent: zapReport?.summary
      });

      if (!zapReport || !zapReport.summary) {
        console.warn(`⚠️ Debug: Missing zapReport or summary:`, {
          hasReport: !!zapReport,
          hasSummary: !!zapReport?.summary
        });
        return null;
      }

      const summary = zapReport.summary;
      const scanContext = zapReport.scanContext || {};

      // Build vulnerability description with summary and scan context
      const description = [
        '=== DAST Security Scan Summary ===',
        `Total Issues Found: ${summary.totalIssues || 0}`,
        `• High Risk: ${summary.highRisk || 0}`,
        `• Medium Risk: ${summary.mediumRisk || 0}`,
        `• Low Risk: ${summary.lowRisk || 0}`,
        `• Informational: ${summary.informational || 0}`,
        '',
        `URLs Scanned: ${summary.urlsScanned || 0}`,
        '',
        '=== Scan Context ===',
        `Sites Tested: ${(scanContext.sites || []).length}`,
        ...(scanContext.sites || []).slice(0, 10).map((site: string) => `• ${site}`),
        ...(scanContext.sites && scanContext.sites.length > 10 ? [`... and ${scanContext.sites.length - 10} more sites`] : []),
        '',
        `Hosts Analyzed: ${(scanContext.hosts || []).length}`,
        ...(scanContext.hosts || []).slice(0, 10).map((host: string) => `• ${host}`),
        ...(scanContext.hosts && scanContext.hosts.length > 10 ? [`... and ${scanContext.hosts.length - 10} more hosts`] : []),
        '',
        '=== Risk Assessment ===',
        summary.highRisk > 0 ? '🔴 HIGH RISK vulnerabilities detected - Immediate attention required' : '',
        summary.mediumRisk > 0 ? '🟡 MEDIUM RISK vulnerabilities detected - Should be addressed' : '',
        summary.lowRisk > 0 ? '🟢 LOW RISK vulnerabilities detected - Consider addressing' : '',
        summary.totalIssues === 0 ? '✅ No security vulnerabilities detected' : '',
        '',
        `Scan completed at: ${zapReport.timestamp || new Date().toISOString()}`,
        `Client ID: ${zapReport.clientId || 'N/A'}`,
        '',
        'For detailed vulnerability information, see the full ZAP security report.'
      ].filter(line => line !== '').join('\n');

      return description;
    } catch (error) {
      console.error('Error generating vulnerability description:', error);
      return null;
    }
  }
}
