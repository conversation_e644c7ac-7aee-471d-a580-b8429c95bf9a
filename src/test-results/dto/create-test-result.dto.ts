import { IsNotEmpty, IsString, IsOptional, IsEnum, IsNumber, IsUUID, IsArray } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TestResultStatus } from '../test-result.entity';

export class CreateTestResultDto {
  @ApiPropertyOptional({
    description: 'Test case ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsOptional()
  testCaseId?: string;

  @ApiProperty({
    description: 'Test result status',
    enum: TestResultStatus,
    example: TestResultStatus.PASSED
  })
  @IsEnum(TestResultStatus)
  @IsNotEmpty()
  status: TestResultStatus;

  @ApiPropertyOptional({
    description: 'Actual test result',
    example: 'User was successfully redirected to dashboard'
  })
  @IsString()
  @IsOptional()
  actualResult?: string;

  @ApiPropertyOptional({
    description: 'Test execution duration in milliseconds',
    example: 15000
  })
  @IsNumber()
  @IsOptional()
  duration?: number;

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Test failed due to network timeout'
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Vulnerability description for DAST security testing',
    example: 'SQL injection vulnerability found in login form'
  })
  @IsString()
  @IsOptional()
  vulnerabilityDescription?: string;

  @ApiPropertyOptional({
    description: 'Original severity as reported by DAST tool',
    example: 'High'
  })
  @IsString()
  @IsOptional()
  originalSeverity?: string;

  @ApiPropertyOptional({
    description: 'Adjusted severity after manual review',
    example: 'Medium'
  })
  @IsString()
  @IsOptional()
  adjustedSeverity?: string;

  @ApiPropertyOptional({
    description: 'URLs affected by the security vulnerability',
    example: 'https://example.com/login\nhttps://example.com/register'
  })
  @IsString()
  @IsOptional()
  affectedUrls?: string;

  @ApiPropertyOptional({
    description: 'HTTP request and response details for DAST testing',
    example: 'GET /api/users HTTP/1.1\nHost: example.com\n\nHTTP/1.1 200 OK\nContent-Type: application/json'
  })
  @IsString()
  @IsOptional()
  requestResponse?: string;

  @ApiPropertyOptional({
    description: 'Remediation guidance for fixing the security vulnerability',
    example: 'Use parameterized queries to prevent SQL injection attacks'
  })
  @IsString()
  @IsOptional()
  remediationGuidance?: string;

  @ApiPropertyOptional({
    description: 'Screenshot URL',
    example: 'https://storage.example.com/screenshots/test-123.png'
  })
  @IsString()
  @IsOptional()
  screenshotUrl?: string;

  @ApiPropertyOptional({
    description: 'Video recording URL',
    example: 'https://storage.example.com/videos/test-123.mp4'
  })
  @IsString()
  @IsOptional()
  videoUrl?: string;

  @ApiPropertyOptional({
    description: 'Google Cloud Storage URL for full logs',
    example: 'gs://agentq-test-logs/test-123/logs.json'
  })
  @IsString()
  @IsOptional()
  logsUrl?: string;

  @ApiPropertyOptional({
    description: 'Array of log messages for test execution',
    example: ['🚀 Starting test execution...', '✅ Test completed successfully']
  })
  @IsArray()
  @IsOptional()
  logs?: string[];

  @ApiPropertyOptional({
    description: 'The name or email of the user who created this test result',
    example: '<EMAIL>'
  })
  @IsString()
  @IsOptional()
  createdBy?: string;
}