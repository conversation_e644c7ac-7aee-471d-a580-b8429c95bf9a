import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { TestRun } from '../test-runs/test-run.entity';
import { TestCase } from '../test-cases/test-case.entity';

export enum TestResultStatus {
  PASSED = 'passed',
  FAILED = 'failed',
  BLOCKED = 'blocked',
  SKIPPED = 'skipped',
  UNTESTED = 'untested',
  // DAST Security Status
  NEW = 'New',
  OPEN = 'Open',
  IN_PROGRESS = 'In Progress',
  FIXED_REMEDIATED = 'Fixed/Remediated',
  VERIFIED_RETESTED = 'Verified/Re-tested',
  CLOSED = 'Closed',
  FALSE_POSITIVE = 'False Positive',
  ACCEPTED_RISK = 'Accepted Risk / Waived',
  DUPLICATE = 'Duplicate'
}

@Entity('test_results')
export class TestResult {
  @ApiProperty({
    description: 'The unique identifier of the test result',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => TestRun, { onDelete: 'CASCADE' })
  testRun: TestRun;

  @Column()
  testRunId: string;

  @ManyToOne(() => TestCase, { onDelete: 'CASCADE' })
  testCase: TestCase;

  @Column()
  testCaseId: string;

  @ApiProperty({
    description: 'Test result status',
    enum: TestResultStatus,
    example: TestResultStatus.PASSED
  })
  @Column({
    type: 'enum',
    enum: TestResultStatus,
    default: TestResultStatus.UNTESTED
  })
  status: TestResultStatus;

  @ApiProperty({
    description: 'Actual test result',
    example: 'User was successfully redirected to dashboard'
  })
  @Column('text', { nullable: true })
  actualResult: string;

  @ApiProperty({
    description: 'Test execution duration in milliseconds',
    example: 15000
  })
  @Column('int', { nullable: true })
  duration: number;

  @ApiProperty({
    description: 'Additional notes',
    example: 'Test failed due to network timeout'
  })
  @Column('text', { nullable: true })
  notes: string;

  @ApiProperty({
    description: 'Vulnerability description for DAST security testing',
    example: 'SQL injection vulnerability found in login form'
  })
  @Column('text', { nullable: true })
  vulnerabilityDescription: string;

  @ApiProperty({
    description: 'Original severity as reported by DAST tool',
    example: 'High'
  })
  @Column({ nullable: true })
  originalSeverity: string;

  @ApiProperty({
    description: 'Adjusted severity after manual review',
    example: 'Medium'
  })
  @Column({ nullable: true })
  adjustedSeverity: string;

  @ApiProperty({
    description: 'URLs affected by the security vulnerability',
    example: 'https://example.com/login\nhttps://example.com/register'
  })
  @Column('text', { nullable: true })
  affectedUrls: string;

  @ApiProperty({
    description: 'HTTP request and response details for DAST testing',
    example: 'GET /api/users HTTP/1.1\nHost: example.com\n\nHTTP/1.1 200 OK\nContent-Type: application/json'
  })
  @Column('text', { nullable: true })
  requestResponse: string;

  @ApiProperty({
    description: 'Remediation guidance for fixing the security vulnerability',
    example: 'Use parameterized queries to prevent SQL injection attacks'
  })
  @Column('text', { nullable: true })
  remediationGuidance: string;

  @ApiProperty({
    description: 'Screenshot URL',
    example: 'https://storage.example.com/screenshots/test-123.png'
  })
  @Column({ nullable: true })
  screenshotUrl: string;

  @ApiProperty({
    description: 'Video recording URL',
    example: 'https://storage.example.com/videos/test-123.mp4'
  })
  @Column({ nullable: true })
  videoUrl: string;

  @ApiProperty({
    description: 'Google Cloud Storage URL for full logs',
    example: 'gs://agentq-test-logs/test-123/logs.json'
  })
  @Column({ nullable: true })
  logsUrl: string;

  @ApiProperty({
    description: 'Google Cloud Storage URL for ZAP security report (DAST)',
    example: 'gs://agentq/test-results/logs-dast/test-123/zap-report.json'
  })
  @Column({ nullable: true, length: 500 })
  logsSecurityUrl: string;

  @ApiProperty({
    description: 'Sequence number of this result within the test case and test run',
    example: 1
  })
  @Column({ default: 1 })
  sequence: number;

  @ApiProperty({
    description: 'Reference to the previous test result for this test case in this test run',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ nullable: true })
  previousResultId: string;

  @ManyToOne(() => TestResult, { nullable: true })
  previousResult: TestResult;

  @OneToMany(() => TestResult, testResult => testResult.previousResult)
  nextResults: TestResult[];

  @ApiProperty({
    description: 'Whether this is the latest result for this test case in this test run',
    example: true
  })
  @Column({ default: true })
  isLatest: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'The name or email of the user who created this test result',
    example: '<EMAIL>'
  })
  @Column({ nullable: true })
  createdBy: string;
}
